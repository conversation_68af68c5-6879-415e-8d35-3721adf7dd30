<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_conversations', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->index(); // Bỏ unique, chỉ để index
            $table->string('customer_name')->nullable();
            $table->string('customer_phone')->nullable();
            $table->text('message');
            $table->enum('sender', ['user', 'bot']);
            $table->json('customer_data')->nullable(); // Lưu thông tin thu thập được
            $table->enum('conversation_stage', [
                'greeting', 'title', 'name', 'age', 'occupation', 'income', 'cash_flow', 'analysis', 'completed'
            ])->default('greeting');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_conversations');
    }
};
