<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_info', function (Blueprint $table) {
            $table->id();
            $table->string('session_id');
            $table->string('name');
            $table->string('phone');
            $table->integer('age')->nullable();
            $table->string('occupation')->nullable();
            $table->decimal('monthly_income', 15, 2)->nullable();
            $table->text('cash_flow_info')->nullable();
            $table->text('insurance_recommendation')->nullable();
            $table->enum('status', ['collecting', 'completed'])->default('collecting');
            $table->timestamps();

            $table->index('session_id');
            $table->index('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_info');
    }
};
