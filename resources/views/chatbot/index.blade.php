<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Tư vấn bảo hiểm AIA miễn phí 24/7 cùng chuyên gia Hạnh. Tìm gói bảo hiểm phù hợp với thu nhập và nhu cầu của bạn.">
    <meta name="keywords" content="bảo hiểm AIA, tư vấn bảo hiểm, bả<PERSON> hiể<PERSON> nhân thọ, bả<PERSON> hiể<PERSON> sức khỏe, AIA Việt Nam">
    <meta name="author" content="AIA Việt Nam">
    <title>Tư vấn bảo hiểm AIA - Chuyên gia Hạnh | Tư vấn miễn phí 24/7</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 100%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .consultant-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 10px;
        }

        .consultant-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: #e53e3e;
            font-weight: bold;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.bot {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            position: relative;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin: 0 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .message.user .message-avatar {
            background: #007bff;
            color: white;
            order: 2;
        }

        .message.bot .message-avatar {
            background: #e53e3e;
            color: white;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
            text-align: center;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 8px;
            border: 2px solid #e0e0e0;
            transition: border-color 0.3s;
        }

        .input-group:focus-within {
            border-color: #007bff;
        }

        .message-input {
            flex: 1;
            border: none;
            outline: none;
            padding: 8px 16px;
            font-size: 14px;
            background: transparent;
            resize: none;
            max-height: 100px;
        }

        .send-button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            padding: 10px 16px;
            background: white;
            border-radius: 18px;
            border: 1px solid #e0e0e0;
            margin-bottom: 15px;
            max-width: 70%;
        }

        .typing-dots {
            display: flex;
            align-items: center;
        }

        .typing-dots span {
            height: 8px;
            width: 8px;
            background: #999;
            border-radius: 50%;
            display: inline-block;
            margin-right: 4px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .welcome-message i {
            font-size: 48px;
            color: #e53e3e;
            margin-bottom: 20px;
        }

        .footer-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
        }

        .admin-link {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.1);
            color: transparent;
            padding: 5px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 10px;
            z-index: 1000;
            transition: all 0.3s;
        }

        .admin-link:hover {
            background: rgba(0,0,0,0.8);
            color: white;
        }

        @media (max-width: 768px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
                max-width: 100%;
            }

            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1><i class="fas fa-shield-alt"></i> Tư vấn bảo hiểm AIA</h1>
            <p>Chuyên gia tư vấn chuyên nghiệp</p>
            <div class="consultant-info">
                <div class="consultant-avatar">H</div>
                <div>
                    <strong>Chuyên gia Hạnh</strong><br>
                    <small>10 năm kinh nghiệm tư vấn bảo hiểm</small>
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <i class="fas fa-shield-alt"></i>
                <h3>Chào mừng bạn đến với AIA Việt Nam!</h3>
                <p>Tư vấn bảo hiểm miễn phí 24/7 cùng chuyên gia Hạnh</p>
                <small class="text-muted">✨ Bảo vệ tương lai - An tâm hôm nay ✨</small>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="message-avatar">H</div>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-group">
                <textarea
                    class="message-input"
                    id="messageInput"
                    placeholder="Nhập tin nhắn của bạn..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Footer info -->
    <div class="footer-info">
        <i class="fas fa-phone"></i> Hotline: 1800 1588 |
        <i class="fas fa-envelope"></i> <EMAIL>
    </div>

    <!-- Hidden admin link -->
    <a href="{{ route('admin.dashboard') }}" class="admin-link">Admin</a>

    <script>
        let sessionId = null;
        let isTyping = false;

        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendButton.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isTyping) return;

            // Add user message to chat
            addMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show typing indicator
            showTyping();

            // Send to server
            fetch('/chatbot/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId
                })
            })
            .then(response => response.json())
            .then(data => {
                hideTyping();
                if (data.success) {
                    sessionId = data.session_id;
                    addMessage(data.response, 'bot');
                } else {
                    addMessage('Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.', 'bot');
                }
            })
            .catch(error => {
                hideTyping();
                console.error('Error:', error);
                addMessage('Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.', 'bot');
            });
        }

        function addMessage(content, sender) {
            // Remove welcome message if exists
            const welcomeMessage = chatMessages.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const now = new Date();
            const timeString = now.toLocaleTimeString('vi-VN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.innerHTML = `
                <div class="message-avatar">${sender === 'user' ? 'B' : 'H'}</div>
                <div class="message-content">
                    ${content}
                    <div class="message-time">${timeString}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTyping() {
            isTyping = true;
            sendButton.disabled = true;
            typingIndicator.style.display = 'flex';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTyping() {
            isTyping = false;
            sendButton.disabled = false;
            typingIndicator.style.display = 'none';
        }

        // Initialize chat with greeting
        window.addEventListener('load', function() {
            setTimeout(() => {
                sendMessage = function() {
                    const message = messageInput.value.trim();
                    if (!message || isTyping) return;

                    addMessage(message, 'user');
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    showTyping();

                    fetch('/chatbot/send', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            message: message,
                            session_id: sessionId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideTyping();
                        if (data.success) {
                            sessionId = data.session_id;
                            addMessage(data.response, 'bot');
                        } else {
                            addMessage('Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.', 'bot');
                        }
                    })
                    .catch(error => {
                        hideTyping();
                        console.error('Error:', error);
                        addMessage('Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.', 'bot');
                    });
                };

                // Auto start conversation
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    addMessage('Xin chào! Tôi là Hạnh, chuyên gia tư vấn bảo hiểm của AIA với 10 năm kinh nghiệm. Tôi rất vui được hỗ trợ bạn tìm hiểu về các sản phẩm bảo hiểm phù hợp. Để tôi có thể tư vấn chính xác nhất, bạn có thể cho tôi biết tên của bạn không?', 'bot');
                }, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
