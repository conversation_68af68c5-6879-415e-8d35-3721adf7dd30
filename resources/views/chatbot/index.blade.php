<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="Tư vấn bảo hiểm AIA miễn phí 24/7 cùng chuyên gia Hạnh. Tìm gói bảo hiểm phù hợp với thu nhập và nhu cầu của bạn.">
    <meta name="keywords" content="bảo hiểm AIA, tư vấn bảo hiểm, bả<PERSON> hiể<PERSON> nhân thọ, bảo hiể<PERSON> sức khỏe, AIA Việt Nam">
    <meta name="author" content="AIA Việt Nam">
    <title>Tư vấn bảo hiểm AIA - Chuyên gia Hạnh | Tư vấn miễn phí 24/7</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
        }

        .chat-container {
            width: 100%;
            max-width: 900px;
            height: 85vh;
            background: white;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }

        .chat-header {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
            position: relative;
            border-radius: 24px 24px 0 0;
        }

        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .chat-header h1 {
            font-size: 28px;
            margin-bottom: 8px;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }

        .chat-header p {
            font-size: 15px;
            opacity: 0.95;
            position: relative;
            z-index: 1;
        }

        .consultant-info {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
            position: relative;
            z-index: 1;
        }

        .consultant-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: #e53e3e;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .chat-messages {
            flex: 1;
            padding: 25px 30px;
            overflow-y: auto;
            background: linear-gradient(to bottom, #f8f9fa 0%, #ffffff 100%);
            position: relative;
        }

        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.bot {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 75%;
            padding: 16px 20px;
            border-radius: 20px;
            font-size: 15px;
            line-height: 1.5;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-bottom-right-radius: 6px;
        }

        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e8e8e8;
            border-bottom-left-radius: 6px;
        }

        .message-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin: 0 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            flex-shrink: 0;
        }

        .message.user .message-avatar {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            order: 2;
        }

        .message.bot .message-avatar {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
        }

        .message-time {
            font-size: 11px;
            color: rgba(255,255,255,0.7);
            margin-top: 6px;
            text-align: right;
        }

        .message.bot .message-time {
            color: #999;
            text-align: left;
        }

        .chat-input {
            padding: 25px 30px;
            background: white;
            border-top: 1px solid #e8e8e8;
            border-radius: 0 0 24px 24px;
        }

        .input-group {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 30px;
            padding: 12px;
            border: 2px solid #e8e8e8;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .input-group:focus-within {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
            transform: translateY(-1px);
        }

        .message-input {
            flex: 1;
            border: none;
            outline: none;
            padding: 12px 20px;
            font-size: 15px;
            background: transparent;
            resize: none;
            max-height: 120px;
            font-family: inherit;
        }

        .message-input::placeholder {
            color: #999;
        }

        .send-button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
        }

        .send-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,123,255,0.4);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .typing-indicator {
            display: none;
            padding: 16px 20px;
            background: white;
            border-radius: 20px;
            border: 1px solid #e8e8e8;
            margin-bottom: 20px;
            max-width: 75%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            animation: fadeInUp 0.3s ease-out;
        }

        .typing-dots {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .typing-dots span {
            height: 10px;
            width: 10px;
            background: #999;
            border-radius: 50%;
            display: inline-block;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.4;
            }
            30% {
                transform: translateY(-8px);
                opacity: 1;
            }
        }

        .welcome-message {
            text-align: center;
            padding: 60px 30px;
            color: #666;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }

        .welcome-message i {
            font-size: 64px;
            color: #e53e3e;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-message h3 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .welcome-message p {
            font-size: 16px;
            margin-bottom: 10px;
            color: #666;
        }

        .welcome-message small {
            font-size: 14px;
            color: #999;
            font-style: italic;
        }

        .footer-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
        }

        .admin-link {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.1);
            color: transparent;
            padding: 5px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 10px;
            z-index: 1000;
            transition: all 0.3s;
        }

        .admin-link:hover {
            background: rgba(0,0,0,0.8);
            color: white;
        }

        @media (max-width: 768px) {
            body {
                padding: 0;
            }

            .chat-container {
                height: 100vh;
                border-radius: 0;
                max-width: 100%;
            }

            .chat-header {
                padding: 20px 20px;
                border-radius: 0;
            }

            .chat-header h1 {
                font-size: 24px;
            }

            .consultant-avatar {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }

            .chat-messages {
                padding: 20px 15px;
            }

            .message-content {
                max-width: 85%;
                padding: 14px 18px;
                font-size: 14px;
            }

            .chat-input {
                padding: 20px 15px;
            }

            .input-group {
                padding: 10px;
            }

            .message-input {
                padding: 10px 16px;
                font-size: 14px;
            }

            .send-button {
                width: 44px;
                height: 44px;
            }

            .footer-info {
                font-size: 11px;
                padding: 6px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1><i class="fas fa-shield-alt"></i> Tư vấn bảo hiểm AIA</h1>
            <p>Chuyên gia tư vấn chuyên nghiệp</p>
            <div class="consultant-info">
                <div class="consultant-avatar">H</div>
                <div>
                    <strong>Chuyên gia Hạnh</strong><br>
                    <small>10 năm kinh nghiệm tư vấn bảo hiểm</small>
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                <i class="fas fa-shield-alt"></i>
                <h3>Chào mừng bạn đến với AIA Việt Nam!</h3>
                <p>Tư vấn bảo hiểm miễn phí 24/7 cùng chuyên gia Hạnh</p>
                <small class="text-muted">✨ Bảo vệ tương lai - An tâm hôm nay ✨</small>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="message-avatar">H</div>
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-group">
                <textarea
                    class="message-input"
                    id="messageInput"
                    placeholder="Nhập tin nhắn của bạn..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Footer info -->
    <div class="footer-info">
        <i class="fas fa-phone"></i> Hotline: 1800 1588 |
        <i class="fas fa-envelope"></i> <EMAIL>
    </div>

    <!-- Hidden admin link -->
    <a href="{{ route('admin.dashboard') }}" class="admin-link">Admin</a>

    <script>
        let sessionId = null;
        let isTyping = false;

        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendButton.addEventListener('click', sendMessage);

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isTyping) return;

            // Add user message to chat
            addMessage(message, 'user');
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show typing indicator
            showTyping();

            // Send to server
            fetch('/chatbot/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId
                })
            })
            .then(response => response.json())
            .then(data => {
                hideTyping();
                if (data.success) {
                    sessionId = data.session_id;
                    addMessage(data.response, 'bot');
                } else {
                    // Hiển thị tin nhắn lỗi từ server hoặc tin nhắn mặc định
                    const errorMessage = data.message || 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.';
                    addMessage(errorMessage, 'bot');
                }
            })
            .catch(error => {
                hideTyping();
                console.error('Error:', error);
                addMessage('Xin lỗi, có lỗi kết nối. Vui lòng kiểm tra mạng và thử lại.', 'bot');
            });
        }

        function addMessage(content, sender) {
            // Remove welcome message if exists
            const welcomeMessage = chatMessages.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const now = new Date();
            const timeString = now.toLocaleTimeString('vi-VN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.innerHTML = `
                <div class="message-avatar">${sender === 'user' ? 'B' : 'H'}</div>
                <div class="message-content">
                    ${content}
                    <div class="message-time">${timeString}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTyping() {
            isTyping = true;
            sendButton.disabled = true;
            typingIndicator.style.display = 'flex';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTyping() {
            isTyping = false;
            sendButton.disabled = false;
            typingIndicator.style.display = 'none';
        }

        // Initialize chat with greeting
        window.addEventListener('load', function() {
            setTimeout(() => {
                sendMessage = function() {
                    const message = messageInput.value.trim();
                    if (!message || isTyping) return;

                    addMessage(message, 'user');
                    messageInput.value = '';
                    messageInput.style.height = 'auto';
                    showTyping();

                    fetch('/chatbot/send', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            message: message,
                            session_id: sessionId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideTyping();
                        if (data.success) {
                            sessionId = data.session_id;
                            addMessage(data.response, 'bot');
                        } else {
                            const errorMessage = data.message || 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại.';
                            addMessage(errorMessage, 'bot');
                        }
                    })
                    .catch(error => {
                        hideTyping();
                        console.error('Error:', error);
                        addMessage('Xin lỗi, có lỗi kết nối. Vui lòng kiểm tra mạng và thử lại.', 'bot');
                    });
                };

                // Auto start conversation
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    addMessage('Xin chào! Em là Hạnh, chuyên gia tư vấn bảo hiểm của AIA với 10 năm kinh nghiệm 😊 Em rất vui được hỗ trợ bạn tìm hiểu về các sản phẩm bảo hiểm phù hợp. Trước tiên, em có thể hỏi bạn muốn được gọi là anh hay chị ạ?', 'bot');
                }, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
