<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chi tiết khách hàng - {{ $customer->name }}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .info-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .chat-message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
        }
        .chat-message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }
        .chat-message.bot {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }
        .chat-container {
            max-height: 500px;
            overflow-y: auto;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        .message-time {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar text-white p-4">
                <h4 class="mb-4"><i class="fas fa-shield-alt"></i> AIA Admin</h4>
                <ul class="nav flex-column">
                    <li class="nav-item mb-2">
                        <a class="nav-link text-white" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a class="nav-link text-white" href="{{ route('home') }}">
                            <i class="fas fa-comments me-2"></i> Chatbot
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a class="nav-link text-white" href="{{ route('admin.export') }}">
                            <i class="fas fa-download me-2"></i> Xuất dữ liệu
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-sm me-2">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        <h2 class="d-inline">Chi tiết khách hàng</h2>
                    </div>
                    <div>
                        <span class="text-muted">{{ $customer->created_at->format('d/m/Y H:i') }}</span>
                    </div>
                </div>

                <div class="row">
                    <!-- Customer Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-user me-2"></i>Thông tin khách hàng</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Họ tên:</strong></div>
                                    <div class="col-sm-8">{{ $customer->name ?: 'Chưa cập nhật' }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Số điện thoại:</strong></div>
                                    <div class="col-sm-8">
                                        @if($customer->phone)
                                            <a href="tel:{{ $customer->phone }}">{{ $customer->phone }}</a>
                                        @else
                                            <span class="text-muted">Chưa có</span>
                                        @endif
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Tuổi:</strong></div>
                                    <div class="col-sm-8">{{ $customer->age ?: 'Chưa cập nhật' }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Nghề nghiệp:</strong></div>
                                    <div class="col-sm-8">{{ $customer->occupation ?: 'Chưa cập nhật' }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Thu nhập/tháng:</strong></div>
                                    <div class="col-sm-8">
                                        @if($customer->monthly_income)
                                            {{ number_format($customer->monthly_income) }} VNĐ
                                        @else
                                            Chưa cập nhật
                                        @endif
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Trạng thái:</strong></div>
                                    <div class="col-sm-8">
                                        @if($customer->status === 'completed')
                                            <span class="badge bg-success">Hoàn thành</span>
                                        @else
                                            <span class="badge bg-warning">Đang thu thập</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card info-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Thông tin tài chính</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Thông tin dòng tiền:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        {{ $customer->cash_flow_info ?: 'Chưa cập nhật' }}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <strong>Tư vấn bảo hiểm:</strong>
                                    <div class="mt-2 p-3 bg-light rounded">
                                        {{ $customer->insurance_recommendation ?: 'Chưa có tư vấn' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat History -->
                <div class="row">
                    <div class="col-12">
                        <div class="card info-card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="fas fa-comments me-2"></i>Lịch sử hội thoại</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="chat-container">
                                    @forelse($customer->conversations->sortBy('created_at') as $conversation)
                                        <div class="chat-message {{ $conversation->sender }}">
                                            <div class="message-content">
                                                {{ $conversation->message }}
                                                <div class="message-time">
                                                    {{ $conversation->created_at->format('d/m/Y H:i:s') }}
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="text-center py-4">
                                            <i class="fas fa-comments fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">Chưa có cuộc hội thoại nào</p>
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        @if($customer->phone)
                            <a href="tel:{{ $customer->phone }}" class="btn btn-success me-2">
                                <i class="fas fa-phone me-1"></i> Gọi điện
                            </a>
                        @endif
                        <a href="mailto:{{ $customer->email ?? '' }}" class="btn btn-primary me-2">
                            <i class="fas fa-envelope me-1"></i> Gửi email
                        </a>
                        <button class="btn btn-warning" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> In thông tin
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto scroll to bottom of chat
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.querySelector('.chat-container');
            if (chatContainer) {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        });
    </script>
</body>
</html>
