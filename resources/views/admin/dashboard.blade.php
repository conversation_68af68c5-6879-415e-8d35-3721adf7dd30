<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Chatbot T<PERSON> v<PERSON><PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stats-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .customer-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .badge-status {
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        .btn-action {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar text-white p-4">
                <h4 class="mb-4"><i class="fas fa-shield-alt"></i> AIA Admin</h4>
                <ul class="nav flex-column">
                    <li class="nav-item mb-2">
                        <a class="nav-link text-white active" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a class="nav-link text-white" href="{{ route('chatbot.index') }}">
                            <i class="fas fa-comments me-2"></i> Chatbot
                        </a>
                    </li>
                    <li class="nav-item mb-2">
                        <a class="nav-link text-white" href="{{ route('admin.export') }}">
                            <i class="fas fa-download me-2"></i> Xuất dữ liệu
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Dashboard Quản lý</h2>
                    <div>
                        <span class="text-muted">{{ date('d/m/Y H:i') }}</span>
                    </div>
                </div>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h3 class="text-primary">{{ $totalCustomers }}</h3>
                                <p class="text-muted mb-0">Tổng khách hàng</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h3 class="text-success">{{ $completedConsultations }}</h3>
                                <p class="text-muted mb-0">Tư vấn hoàn thành</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-calendar-day fa-2x text-warning mb-2"></i>
                                <h3 class="text-warning">{{ $todayCustomers }}</h3>
                                <p class="text-muted mb-0">Khách hàng hôm nay</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                                <h3 class="text-info">{{ $totalCustomers > 0 ? round(($completedConsultations / $totalCustomers) * 100, 1) : 0 }}%</h3>
                                <p class="text-muted mb-0">Tỷ lệ hoàn thành</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Table -->
                <div class="customer-table">
                    <div class="card-header bg-white p-3 border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Danh sách khách hàng</h5>
                            <a href="{{ route('admin.export') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-download me-1"></i> Xuất Excel
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Họ tên</th>
                                        <th>Số điện thoại</th>
                                        <th>Tuổi</th>
                                        <th>Nghề nghiệp</th>
                                        <th>Thu nhập/tháng</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày tạo</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($customers as $customer)
                                    <tr>
                                        <td>{{ $customer->id }}</td>
                                        <td>
                                            <strong>{{ $customer->name ?: 'Chưa cập nhật' }}</strong>
                                        </td>
                                        <td>
                                            @if($customer->phone)
                                                <a href="tel:{{ $customer->phone }}" class="text-decoration-none">
                                                    {{ $customer->phone }}
                                                </a>
                                            @else
                                                <span class="text-muted">Chưa có</span>
                                            @endif
                                        </td>
                                        <td>{{ $customer->age ?: '-' }}</td>
                                        <td>{{ $customer->occupation ?: '-' }}</td>
                                        <td>
                                            @if($customer->monthly_income)
                                                {{ number_format($customer->monthly_income) }} VNĐ
                                            @else
                                                -
                                            @endif
                                        </td>
                                        <td>
                                            @if($customer->status === 'completed')
                                                <span class="badge badge-status bg-success">Hoàn thành</span>
                                            @else
                                                <span class="badge badge-status bg-warning">Đang thu thập</span>
                                            @endif
                                        </td>
                                        <td>{{ $customer->created_at->format('d/m/Y H:i') }}</td>
                                        <td>
                                            <a href="{{ route('admin.customer', $customer->id) }}" 
                                               class="btn btn-primary btn-action">
                                                <i class="fas fa-eye"></i> Xem
                                            </a>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">Chưa có khách hàng nào</p>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                    @if($customers->hasPages())
                    <div class="card-footer bg-white">
                        {{ $customers->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
