<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ChatbotController;
use App\Http\Controllers\AdminController;

Route::get('/', function () {
    return view('welcome');
});

// Chatbot routes
Route::get('/chatbot', [ChatbotController::class, 'index'])->name('chatbot.index');
Route::post('/chatbot/send', [ChatbotController::class, 'sendMessage'])->name('chatbot.send');
Route::get('/chatbot/history/{sessionId}', [ChatbotController::class, 'getConversationHistory'])->name('chatbot.history');
Route::get('/chatbot/customer/{sessionId}', [ChatbotController::class, 'getCustomerInfo'])->name('chatbot.customer');

// Admin routes
Route::get('/admin', [AdminController::class, 'dashboard'])->name('admin.dashboard');
Route::get('/admin/customer/{id}', [AdminController::class, 'customerDetail'])->name('admin.customer');
Route::get('/admin/export', [AdminController::class, 'exportCustomers'])->name('admin.export');
