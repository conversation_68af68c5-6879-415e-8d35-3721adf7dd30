<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ChatbotController;
use App\Http\Controllers\AdminController;

// Trang chủ hiển thị chatbot
Route::get('/', [ChatbotController::class, 'index'])->name('home');

// Chatbot routes
Route::post('/chatbot/send', [ChatbotController::class, 'sendMessage'])->name('chatbot.send');
Route::get('/chatbot/history/{sessionId}', [ChatbotController::class, 'getConversationHistory'])->name('chatbot.history');
Route::get('/chatbot/customer/{sessionId}', [ChatbotController::class, 'getCustomerInfo'])->name('chatbot.customer');

// Admin routes
Route::get('/admin', [AdminController::class, 'dashboard'])->name('admin.dashboard');
Route::get('/admin/customer/{id}', [AdminController::class, 'customerDetail'])->name('admin.customer');
Route::get('/admin/export', [AdminController::class, 'exportCustomers'])->name('admin.export');
