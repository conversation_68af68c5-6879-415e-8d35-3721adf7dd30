<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ChatConversation extends Model
{
    protected $fillable = [
        'session_id',
        'customer_name',
        'customer_phone',
        'message',
        'sender',
        'customer_data',
        'conversation_stage'
    ];

    protected $casts = [
        'customer_data' => 'array'
    ];

    public function customerInfo()
    {
        return $this->hasOne(CustomerInfo::class, 'session_id', 'session_id');
    }
}
