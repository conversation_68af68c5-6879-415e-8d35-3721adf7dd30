<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CustomerInfo extends Model
{
    protected $table = 'customer_info';

    protected $fillable = [
        'session_id',
        'name',
        'phone',
        'title',
        'age',
        'occupation',
        'monthly_income',
        'cash_flow_info',
        'insurance_recommendation',
        'status'
    ];

    public function conversations()
    {
        return $this->hasMany(ChatConversation::class, 'session_id', 'session_id');
    }
}
