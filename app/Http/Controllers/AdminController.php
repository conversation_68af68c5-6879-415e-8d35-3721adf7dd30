<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\CustomerInfo;
use App\Models\ChatConversation;

class AdminController extends Controller
{
    public function dashboard()
    {
        $customers = CustomerInfo::with('conversations')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $totalCustomers = CustomerInfo::count();
        $completedConsultations = CustomerInfo::where('status', 'completed')->count();
        $todayCustomers = CustomerInfo::whereDate('created_at', today())->count();

        return view('admin.dashboard', compact(
            'customers',
            'totalCustomers',
            'completedConsultations',
            'todayCustomers'
        ));
    }

    public function customerDetail($id)
    {
        $customer = CustomerInfo::with('conversations')->findOrFail($id);

        return view('admin.customer-detail', compact('customer'));
    }

    public function exportCustomers()
    {
        $customers = CustomerInfo::all();

        $filename = 'customers_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($customers) {
            $file = fopen('php://output', 'w');

            // Header
            fputcsv($file, [
                'ID', 'Họ tên', 'Số điện thoại', 'Tuổi', 'Nghề nghiệp',
                'Thu nhập/tháng', 'Thông tin dòng tiền', 'Tư vấn bảo hiểm',
                'Trạng thái', 'Ngày tạo'
            ]);

            foreach ($customers as $customer) {
                fputcsv($file, [
                    $customer->id,
                    $customer->name,
                    $customer->phone,
                    $customer->age,
                    $customer->occupation,
                    number_format($customer->monthly_income),
                    $customer->cash_flow_info,
                    $customer->insurance_recommendation,
                    $customer->status,
                    $customer->created_at->format('d/m/Y H:i')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
