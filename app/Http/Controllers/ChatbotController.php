<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChatConversation;
use App\Models\CustomerInfo;
use App\Services\OpenAIService;
use Illuminate\Support\Str;

class ChatbotController extends Controller
{
    protected $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        $this->openAIService = $openAIService;
    }

    public function index()
    {
        return view('chatbot.index');
    }

    public function sendMessage(Request $request)
    {
        try {
            $request->validate([
                'message' => 'required|string|max:1000',
                'session_id' => 'nullable|string'
            ]);

            $sessionId = $request->session_id ?: Str::uuid();
            $userMessage = $request->message;

            // Đảm bảo có customer info trước khi làm gì khác
            $this->ensureCustomerInfo($sessionId);

            // Lưu tin nhắn của user
            $userConversation = ChatConversation::create([
                'session_id' => $sessionId,
                'message' => $userMessage,
                'sender' => 'user',
                'conversation_stage' => 'greeting'
            ]);

            // L<PERSON><PERSON> lịch sử hội thoại
            $conversations = ChatConversation::where('session_id', $sessionId)
                ->orderBy('created_at', 'asc')
                ->get();

            // Xây dựng context cho AI
            $messages = [];
            foreach ($conversations as $conv) {
                $role = $conv->sender === 'user' ? 'user' : 'assistant';
                $messages[] = [
                    'role' => $role,
                    'content' => $conv->message
                ];
            }

            // Xác định stage hiện tại và xử lý logic
            $currentStage = $this->determineConversationStage($sessionId, $userMessage);
            $this->updateCustomerInfo($sessionId, $userMessage, $currentStage);

            // Tạo response từ AI
            $systemPrompt = $this->openAIService->getInsuranceConsultantPrompt();
            $botResponse = $this->openAIService->generateResponse($messages, $systemPrompt);

            // Lưu response của bot
            ChatConversation::create([
                'session_id' => $sessionId,
                'message' => $botResponse,
                'sender' => 'bot',
                'conversation_stage' => $currentStage
            ]);

            return response()->json([
                'success' => true,
                'session_id' => $sessionId,
                'response' => $botResponse,
                'stage' => $currentStage
            ]);

        } catch (\Exception $e) {
            \Log::error('Chatbot Error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Có lỗi xảy ra, vui lòng thử lại.',
                'message' => 'Xin lỗi, em gặp một chút vấn đề kỹ thuật. Bạn có thể thử gửi lại tin nhắn không ạ?'
            ], 500);
        }
    }

    private function determineConversationStage($sessionId, $userMessage)
    {
        $customerInfo = CustomerInfo::where('session_id', $sessionId)->first();

        // Nếu chưa có thông tin khách hàng, return greeting để tạo mới
        if (!$customerInfo) {
            return 'greeting';
        }

        // Logic xác định stage dựa trên thông tin đã có
        if (empty($customerInfo->title)) {
            return 'title';
        } elseif (empty($customerInfo->name)) {
            return 'name';
        } elseif (empty($customerInfo->age)) {
            return 'age';
        } elseif (empty($customerInfo->occupation)) {
            return 'occupation';
        } elseif (empty($customerInfo->monthly_income)) {
            return 'income';
        } elseif (empty($customerInfo->cash_flow_info)) {
            return 'cash_flow';
        } elseif (empty($customerInfo->insurance_recommendation)) {
            return 'analysis';
        } else {
            return 'completed';
        }
    }

    private function updateCustomerInfo($sessionId, $userMessage, $stage)
    {
        $customerInfo = CustomerInfo::where('session_id', $sessionId)->first();

        if (!$customerInfo) {
            // Tạo mới nếu chưa có
            $customerInfo = CustomerInfo::create([
                'session_id' => $sessionId,
                'name' => '',
                'phone' => '',
                'title' => null
            ]);
        }

        switch ($stage) {
            case 'title':
                // Trích xuất cách xưng hô
                $title = $this->extractTitle($userMessage);
                if ($title) {
                    $customerInfo->title = $title;
                }
                break;

            case 'name':
                // Trích xuất tên từ tin nhắn
                $customerInfo->name = $this->extractName($userMessage);
                break;

            case 'age':
                // Trích xuất tuổi
                $age = $this->extractAge($userMessage);
                if ($age) {
                    $customerInfo->age = $age;
                }
                break;

            case 'occupation':
                $customerInfo->occupation = $userMessage;
                break;

            case 'income':
                $income = $this->extractIncome($userMessage);
                if ($income) {
                    $customerInfo->monthly_income = $income;
                }
                break;

            case 'cash_flow':
                $customerInfo->cash_flow_info = $userMessage;
                break;
        }

        // Trích xuất số điện thoại nếu có trong bất kỳ tin nhắn nào
        $phone = $this->extractPhone($userMessage);
        if ($phone && empty($customerInfo->phone)) {
            $customerInfo->phone = $phone;
        }

        $customerInfo->save();
    }

    private function extractTitle($message)
    {
        $message = strtolower(trim($message));

        // Tìm kiếm từ khóa "anh" hoặc "chị"
        if (strpos($message, 'anh') !== false) {
            return 'anh';
        } elseif (strpos($message, 'chị') !== false || strpos($message, 'chi') !== false) {
            return 'chị';
        }

        return null;
    }

    private function extractName($message)
    {
        // Logic đơn giản để trích xuất tên
        $message = trim($message);

        // Loại bỏ các từ thường gặp
        $commonWords = ['tôi là', 'tên tôi là', 'mình là', 'em là', 'anh là', 'chị là', 'tôi tên', 'mình tên'];

        foreach ($commonWords as $word) {
            if (stripos($message, $word) !== false) {
                $name = trim(str_ireplace($word, '', $message));
                return ucwords(strtolower($name));
            }
        }

        return ucwords(strtolower($message));
    }

    private function extractAge($message)
    {
        // Trích xuất số tuổi từ tin nhắn
        preg_match('/(\d{1,2})/', $message, $matches);

        if (isset($matches[1])) {
            $age = intval($matches[1]);
            if ($age >= 18 && $age <= 100) {
                return $age;
            }
        }

        return null;
    }

    private function extractIncome($message)
    {
        // Trích xuất thu nhập (loại bỏ dấu phẩy, triệu, nghìn)
        $message = str_replace([',', '.', ' '], '', $message);

        preg_match('/(\d+)/', $message, $matches);

        if (isset($matches[1])) {
            $income = intval($matches[1]);

            // Nếu có từ "triệu" thì nhân với 1,000,000
            if (stripos($message, 'triệu') !== false) {
                $income *= 1000000;
            }
            // Nếu có từ "nghìn" thì nhân với 1,000
            elseif (stripos($message, 'nghìn') !== false || stripos($message, 'ngàn') !== false) {
                $income *= 1000;
            }

            return $income;
        }

        return null;
    }

    private function extractPhone($message)
    {
        // Trích xuất số điện thoại Việt Nam
        preg_match('/(0|\+84)[0-9]{9,10}/', $message, $matches);

        if (isset($matches[0])) {
            return $matches[0];
        }

        return null;
    }

    public function getConversationHistory($sessionId)
    {
        $conversations = ChatConversation::where('session_id', $sessionId)
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json([
            'success' => true,
            'conversations' => $conversations
        ]);
    }

    public function getCustomerInfo($sessionId)
    {
        $customerInfo = CustomerInfo::where('session_id', $sessionId)->first();

        return response()->json([
            'success' => true,
            'customer_info' => $customerInfo
        ]);
    }

    private function ensureCustomerInfo($sessionId)
    {
        $customerInfo = CustomerInfo::where('session_id', $sessionId)->first();

        if (!$customerInfo) {
            CustomerInfo::create([
                'session_id' => $sessionId,
                'name' => '',
                'phone' => '',
                'title' => null
            ]);
        }
    }
}
